<template>
  <nav class="bg-white shadow-lg sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <!-- Lo<PERSON> and Brand -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
              <ScissorsIcon class="w-5 h-5 text-white" />
            </div>
            <span class="text-xl font-heading font-bold text-secondary-900">
              Elite Barber
            </span>
          </router-link>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-8">
          <router-link
            v-for="item in navigationItems"
            :key="item.name"
            :to="item.to"
            class="text-secondary-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors"
            :class="{ 'text-primary-600 font-semibold': $route.name === item.name }"
          >
            {{ item.label }}
          </router-link>
        </div>

        <!-- User Menu -->
        <div class="flex items-center space-x-4">
          <!-- Authenticated User Menu -->
          <div v-if="authStore.isAuthenticated" class="relative">
            <Menu as="div" class="relative inline-block text-left">
              <div>
                <MenuButton class="flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                  <img
                    v-if="authStore.user?.avatar"
                    :src="authStore.user.avatar"
                    :alt="authStore.fullName"
                    class="w-8 h-8 rounded-full object-cover"
                  >
                  <div
                    v-else
                    class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center"
                  >
                    <span class="text-white text-sm font-medium">
                      {{ authStore.user?.firstName?.charAt(0) }}{{ authStore.user?.lastName?.charAt(0) }}
                    </span>
                  </div>
                  <span class="hidden sm:block text-secondary-700 font-medium">
                    {{ authStore.fullName }}
                  </span>
                  <ChevronDownIcon class="w-4 h-4 text-secondary-400" />
                </MenuButton>
              </div>

              <transition
                enter-active-class="transition ease-out duration-100"
                enter-from-class="transform opacity-0 scale-95"
                enter-to-class="transform opacity-100 scale-100"
                leave-active-class="transition ease-in duration-75"
                leave-from-class="transform opacity-100 scale-100"
                leave-to-class="transform opacity-0 scale-95"
              >
                <MenuItems class="absolute right-0 mt-2 w-56 origin-top-right bg-white divide-y divide-secondary-100 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <div class="px-4 py-3">
                    <p class="text-sm text-secondary-900 font-medium">{{ authStore.fullName }}</p>
                    <p class="text-sm text-secondary-500">{{ authStore.user?.email }}</p>
                  </div>
                  
                  <div class="py-1">
                    <MenuItem v-slot="{ active }">
                      <router-link
                        to="/profile"
                        :class="[
                          active ? 'bg-secondary-100 text-secondary-900' : 'text-secondary-700',
                          'group flex items-center px-4 py-2 text-sm'
                        ]"
                      >
                        <UserIcon class="mr-3 h-4 w-4" />
                        My Profile
                      </router-link>
                    </MenuItem>
                    
                    <MenuItem v-slot="{ active }">
                      <router-link
                        to="/bookings"
                        :class="[
                          active ? 'bg-secondary-100 text-secondary-900' : 'text-secondary-700',
                          'group flex items-center px-4 py-2 text-sm'
                        ]"
                      >
                        <CalendarIcon class="mr-3 h-4 w-4" />
                        My Bookings
                      </router-link>
                    </MenuItem>
                  </div>
                  
                  <div class="py-1">
                    <MenuItem v-slot="{ active }">
                      <button
                        @click="handleLogout"
                        :class="[
                          active ? 'bg-secondary-100 text-secondary-900' : 'text-secondary-700',
                          'group flex w-full items-center px-4 py-2 text-sm'
                        ]"
                      >
                        <ArrowRightOnRectangleIcon class="mr-3 h-4 w-4" />
                        Sign out
                      </button>
                    </MenuItem>
                  </div>
                </MenuItems>
              </transition>
            </Menu>
          </div>

          <!-- Guest Menu -->
          <div v-else class="flex items-center space-x-4">
            <router-link
              to="/login"
              class="text-secondary-600 hover:text-primary-600 px-3 py-2 text-sm font-medium"
            >
              Sign in
            </router-link>
            <router-link
              to="/register"
              class="btn-primary"
            >
              Sign up
            </router-link>
          </div>

          <!-- Mobile menu button -->
          <div class="md:hidden">
            <button
              @click="mobileMenuOpen = !mobileMenuOpen"
              class="text-secondary-600 hover:text-secondary-900 focus:outline-none focus:text-secondary-900 p-2"
            >
              <Bars3Icon v-if="!mobileMenuOpen" class="w-6 h-6" />
              <XMarkIcon v-else class="w-6 h-6" />
            </button>
          </div>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <div v-show="mobileMenuOpen" class="md:hidden border-t border-secondary-200">
        <div class="px-2 pt-2 pb-3 space-y-1">
          <router-link
            v-for="item in navigationItems"
            :key="item.name"
            :to="item.to"
            @click="mobileMenuOpen = false"
            class="block px-3 py-2 text-base font-medium text-secondary-600 hover:text-primary-600 hover:bg-secondary-50 rounded-md"
            :class="{ 'text-primary-600 bg-primary-50': $route.name === item.name }"
          >
            {{ item.label }}
          </router-link>
        </div>
        
        <!-- Mobile Auth Menu -->
        <div v-if="!authStore.isAuthenticated" class="pt-4 pb-3 border-t border-secondary-200">
          <div class="px-2 space-y-1">
            <router-link
              to="/login"
              @click="mobileMenuOpen = false"
              class="block px-3 py-2 text-base font-medium text-secondary-600 hover:text-primary-600 hover:bg-secondary-50 rounded-md"
            >
              Sign in
            </router-link>
            <router-link
              to="/register"
              @click="mobileMenuOpen = false"
              class="block px-3 py-2 text-base font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md"
            >
              Sign up
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import { Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/vue'
import {
  ScissorsIcon,
  UserIcon,
  CalendarIcon,
  ArrowRightOnRectangleIcon,
  ChevronDownIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/vue/24/outline'

const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()

const mobileMenuOpen = ref(false)

const navigationItems = computed(() => [
  { name: 'Home', label: 'Home', to: '/' },
  { name: 'Services', label: 'Services', to: '/services' },
  { name: 'Barbers', label: 'Our Barbers', to: '/barbers' },
  { name: 'About', label: 'About', to: '/about' },
  { name: 'Contact', label: 'Contact', to: '/contact' }
])

const handleLogout = () => {
  authStore.logout()
  toast.success('Logged out successfully')
  router.push('/')
  mobileMenuOpen.value = false
}
</script>
